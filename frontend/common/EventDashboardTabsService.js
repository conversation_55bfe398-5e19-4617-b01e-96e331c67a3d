angular.module('SportWrench').service('EventDashboardTabsService',EventDashboardTabsService);

EventDashboardTabsService.$inject = [
    'APP_ROUTES', 'EVENT_OPERATIONS', 'EventACLService', 'userService', 'CAMPS_SALES_TYPE', '$state', '$rootScope',
    'SHOW_OFFICIALS_PAYOUTS_TAB_ACTION', 'SHOW_STAFF_PAYOUTS_TAB_ACTION'
];

function EventDashboardTabsService(
    APP_ROUTES, EVENT_OPERATIONS, EventACLService, userService, CAMPS_SALES_TYPE, $state, $rootScope,
    SHOW_OFFICIALS_PAYOUTS_TAB_ACTION, SHOW_STAFF_PAYOUTS_TAB_ACTION
) {
    this.eventData          = null;
    this.APP_ROUTES         = APP_ROUTES;
    this.EVENT_OPERATIONS   = EVENT_OPERATIONS;
    this.EventACLService    = EventACLService;
    this.userService        = userService;
    this.CAMPS_SALES_TYPE   = CAMPS_SALES_TYPE;
    this.$state             = $state;
    this.showOfficialsPayoutsTab = false;
    this.showStaffPayoutsTab = false;

    $rootScope.$on(SHOW_OFFICIALS_PAYOUTS_TAB_ACTION, (e, flag = true) => {
        this.showOfficialsPayoutsTab = flag;
    })

    $rootScope.$on(SHOW_STAFF_PAYOUTS_TAB_ACTION, (e, flag = true) => {
        this.showStaffPayoutsTab = flag;
    })
}

EventDashboardTabsService.prototype.getTabs = function (eventData) {
    this.eventData = eventData;

    return [
        {
            name: 'Teams', states : [
                {
                    name: 'Teams List',
                    state: this.APP_ROUTES.EO.TEAMS,
                    isVisible: () => this.isAllowedByAcl(this.EVENT_OPERATIONS.TEAMS_LIST_TAB)
                },
                {
                    name: 'Payments',
                    state: this.APP_ROUTES.EO.PAYMENTS,
                    isVisible: () => this.isAllowedByAcl(this.EVENT_OPERATIONS.TEAMS_PAYMENTS_TAB)
                },
            ], isVisible: this._teamsTabVisible.bind(this)
        },
        {
            name        : 'Divisions',
            state       : this.APP_ROUTES.EO.DIVISIONS,
            isVisible   : this._divisionsTabVisible.bind(this)
        },
        {
            name        : 'Check In',
            state       : this.APP_ROUTES.EO.CHECK_IN,
            isVisible   : this._checkinTabVisible.bind(this)
        },
        {
            name        : 'Email Module',
            state       : this.APP_ROUTES.EO.EMAIL_MODULE_TMPLS,
            isVisible   : this._emailModuleVisible.bind(this)
        },
        {
            name        : 'Accounting',
            state       : this.APP_ROUTES.EO.TRANSFERS,
            isVisible   : this._isTransfersTabVisible.bind(this)
        },
        {
            name: 'Tickets', states: [
                {
                    name: 'Settings',
                    state: this.APP_ROUTES.EO.TICKETS,
                    isVisible: () => this.isAllowedByAcl(this.EVENT_OPERATIONS.TICKET_SETTINGS_TAB)
                },
                {
                    name: 'Payments List',
                    state: this.APP_ROUTES.EO.TICKETS_PAYMENTS,
                    isVisible: () => this.isAllowedByAcl(this.EVENT_OPERATIONS.TICKET_PAYMENTS_LIST_TAB)
                },
                {
                    name: 'Map',
                    state: this.APP_ROUTES.EO.TICKETS_MAP,
                    isVisible: () => this.isAllowedByAcl(this.EVENT_OPERATIONS.TICKET_MAP_TAB)
                },
                {
                    name: 'Statistics',
                    state: this._accountingTicketsPageRoute(),
                    isVisible: () => this.isAllowedByAcl(this.EVENT_OPERATIONS.TICKET_STATISTICS_TAB)
                },
                {
                    name: 'Discounts',
                    state: this.APP_ROUTES.EO.TICKETS_DISCOUNTS,
                    isVisible: () => this.isAllowedByAcl(this.EVENT_OPERATIONS.TICKET_DISCOUNTS_TAB)
                },
                {
                    name: 'Camps List',
                    state: this.APP_ROUTES.EO.CAMPS,
                    isVisible: this._isCamp.bind(this)
                },
                {
                    name        : 'Coupon List',
                    state       : this.APP_ROUTES.EO.TICKETS_COUPONS,
                    isVisible   : this._couponsListVisible.bind(this)
                },
                {
                    name        : 'Application Approve',
                    state       : this.APP_ROUTES.EO.TICKETS_APP_VERIFICATION_APPROVE,
                    isVisible   : this._ticketsApplicationApproveTabVisible.bind(this)
                }
            ], isVisible: this._ticketsTabVisible.bind(this)
        },
        { name: 'Officials' , state: this.APP_ROUTES.EO.OFFICIALS,  isVisible: this._OfficialsTabVisible.bind(this) },
        { name: 'Staff'     , state: this.APP_ROUTES.EO.STAFFERS,   isVisible: this._StaffTabVisible.bind(this)     },
        {
            name: 'Exhibitors',
            states: [
                { name: 'Applications List', state: this.APP_ROUTES.EO.EXHIBITORS },
                { name: 'Payments List', state: this.APP_ROUTES.EO.EXHIBITORS_PAYMENTS },
                { name: 'Booths', state: this.APP_ROUTES.EO.BOOTHS },
                { name: 'My Exhibitors', state: this.APP_ROUTES.EO.USER_EXHIBITORS },
                { name: 'QR Codes', state: this.APP_ROUTES.EO.EXHIBITORS_TICKETS },
            ],
            isVisible: this._exhibitorTabVisible.bind(this),
        },
        { name: 'Event Info', state: this.APP_ROUTES.EO.INFO,       isVisible: this._eventInfoVisible.bind(this)    },
        { name: 'History'   , state: this.APP_ROUTES.EO.HISTORY,    isVisible: this._historyTabVisible.bind(this)   },
        {
            name: 'Officials Payouts',
            state: this.APP_ROUTES.EO.OFFICIALS_PAYOUTS,
            isVisible: () => this.showOfficialsPayoutsTab,
        },
        {
            name: 'Staff Payouts',
            state: this.APP_ROUTES.EO.STAFF_PAYOUTS,
            isVisible: () => this.showStaffPayoutsTab,
        },
        {
            name: 'Custom Forms',
            state: this.APP_ROUTES.EO.CUSTOM_FORMS,
            isVisible: this._showCustomFormsTab.bind(this),
        },
        {
            name: 'Club Invoices',
            state: this.APP_ROUTES.EO.CLUB_INVOICES,
            isVisible: this._teamsWithClubsTabVisible.bind(this),
        }
    ]
};

EventDashboardTabsService.prototype._couponsListVisible = function () {
    return this.eventData.require_tickets_names
        && (this.eventData.require_coupon || this.eventData.event_ticket_buy_entry_code_required);
};

EventDashboardTabsService.prototype._teamsTabVisible = function () {
    return this._teamsRegAvailable() && (
        this.isAllowedByAcl(this.EVENT_OPERATIONS.TEAMS_LIST_TAB) ||
        this.isAllowedByAcl(this.EVENT_OPERATIONS.TEAMS_PAYMENTS_TAB)
    );
};

EventDashboardTabsService.prototype._teamsWithClubsTabVisible = function () {
    return this._teamsRegAvailable() && this._teamsUseClubsModule()
        && this.isAllowedByAcl(this.EVENT_OPERATIONS.TEAMS_CLUB_INVOICES_TAB);
};

EventDashboardTabsService.prototype._divisionsTabVisible = function () {
    return this._teamsRegAvailable() && this.isAllowedByAcl(this.EVENT_OPERATIONS.DIVISIONS_TAB);
};

EventDashboardTabsService.prototype._checkinTabVisible = function () {
    return this._teamsRegAvailable() && this.isAllowedByAcl(this.EVENT_OPERATIONS.CHECKIN_TAB);
};

EventDashboardTabsService.prototype._teamsRegAvailable = function () {
    return this.eventData.allow_teams_registration;
};

EventDashboardTabsService.prototype._teamsUseClubsModule = function () {
    return this.eventData.teams_use_clubs_module;
};

EventDashboardTabsService.prototype._emailModuleVisible = function  () {
    return this.isAllowedByAcl(this.EVENT_OPERATIONS.EMAIL_MODULE_TAB);
};

EventDashboardTabsService.prototype._eventInfoVisible = function  () {
    return this.isAllowedByAcl(this.EVENT_OPERATIONS.EVENT_INFO_TAB);
};

EventDashboardTabsService.prototype._showCustomFormsTab = function () {
    return this.isAllowedByAcl(this.EVENT_OPERATIONS.CUSTOM_FORMS);
}

EventDashboardTabsService.prototype._isCamp = function() {
    return this.eventData.sales_type === this.CAMPS_SALES_TYPE
        && this.isAllowedByAcl(this.EVENT_OPERATIONS.TICKETS_TAB);
};

EventDashboardTabsService.prototype._ticketsTabVisible = function () {
    return this.eventData.allow_ticket_sales && this.isAllowedByAcl(this.EVENT_OPERATIONS.TICKETS_TAB);
};

EventDashboardTabsService.prototype._OfficialsTabVisible = function () {
    return this.eventData.has_officials && this.isAllowedByAcl(this.EVENT_OPERATIONS.OFFICIALS_TAB);
};

EventDashboardTabsService.prototype._StaffTabVisible = function () {
    return this.eventData.has_staff && this.isAllowedByAcl(this.EVENT_OPERATIONS.STAFF_TAB);
};

EventDashboardTabsService.prototype._exhibitorTabVisible = function () {
    return this.eventData.has_exhibitors && this.isAllowedByAcl(this.EVENT_OPERATIONS.EXHIBITORS_TAB);
};

EventDashboardTabsService.prototype._historyTabVisible = function () {
    return this.eventData.sales_type !== this.CAMPS_SALES_TYPE
        && this._teamsRegAvailable()
        && this.isAllowedByAcl(this.EVENT_OPERATIONS.HISTORY_TAB);
};

EventDashboardTabsService.prototype._ticketsApplicationApproveTabVisible = function () {
    return !this.eventData.hide_ticket_application_approve
        && this.isAllowedByAcl(this.EVENT_OPERATIONS.TICKET_APPLICATION_APPROVE_TAB)
        && this.eventData.require_tickets_names;
}

EventDashboardTabsService.prototype.isAllowedByAcl = function (permission) {
    let userPermissions = this.EventACLService.getUserAcl();

    if(!userPermissions || this.userService.hasGodRole()) {
        return true;
    }

    return this.EventACLService.isOperationAllowed(userPermissions, permission);
};

EventDashboardTabsService.prototype._isTransfersTabVisible = function () {
return this.userService.isEventOwner() && this.isAllowedByAcl(this.EVENT_OPERATIONS.ACCOUNTING_TAB)
        && !(this.eventData.teams_fee === null && this._teamsRegAvailable());
};

EventDashboardTabsService.prototype._accountingTicketsPageRoute = function () {
    return (this.eventData.sales_type === this.CAMPS_SALES_TYPE)
        ? this.APP_ROUTES.EO.CAMPS_STATS
        : this.APP_ROUTES.EO.TICKETS_STATS;
};

EventDashboardTabsService.prototype.goToAvailableTab = function (parentTab, tabsList) {
    for(let i = 0; tabsList.length > i; i++) {
        let tab = tabsList[i];

        if(tab.state) {
            if((tab.isVisible)?tab.isVisible():(parentTab && parentTab.isVisible())) {
                this.$state.go(tab.state, {event: this.eventData.event_id});
                return true;
            }
        } else {
            let success = this.goToAvailableTab(tab, tab.states);

            if(success) {
                return true;
            }
        }
    }
};

EventDashboardTabsService.prototype.findTabByState = function(route, eventData) {
    const tabs = this.getTabs(eventData);

    return findTab(tabs, route);
}


function findTab(tabs, state, parentTab) {
    for (let i = 0; i < tabs.length; i++) {
        const tab = tabs[i];

        if (Array.isArray(tab.states)) {
          const _tab = findTab(tab.states, state, tab);

          if (_tab) {
            return _tab;
          }
        }

        if (tab.state === state) {
          return { tab, parentTab };
        }
      }
}
